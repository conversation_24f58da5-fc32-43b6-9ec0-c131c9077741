package org.example.messaging;

import org.agrona.MutableDirectBuffer;
import org.example.sbe.ExecutionReportEncoder;
import org.example.sbe.MessageHeaderEncoder;

/**
 * Adapter that makes ExecutionReportEncoder implement MessageFlyweight interface.
 * This allows ExecutionReportEncoder to be used polymorphically with the SbeMessagePublisher.
 */
public class ExecutionReportEncoderAdapter extends ExecutionReportEncoder implements MessageFlyweight {
    
    @Override
    public MessageFlyweight wrapAndApplyHeader(MutableDirectBuffer buffer, int offset, MessageHeaderEncoder headerEncoder) {
        super.wrapAndApplyHeader(buffer, offset, headerEncoder);
        return this;
    }
    
    // All other methods are inherited from ExecutionReportEncoder and already implement the required functionality
    // sbeTemplateId(), encodedLength(), sbeBlockLength(), sbeSchemaId(), sbeSchemaVersion() are all available
}
