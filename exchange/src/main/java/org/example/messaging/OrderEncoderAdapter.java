package org.example.messaging;

import org.agrona.MutableDirectBuffer;
import org.example.sbe.MessageHeaderEncoder;
import org.example.sbe.OrderEncoder;
import org.example.sbe.OrderType;

/**
 * Adapter that makes OrderEncoder implement MessageFlyweight interface.
 * This allows OrderEncoder to be used polymorphically with the SbeMessagePublisher.
 */
public class OrderEncoderAdapter extends OrderEncoder implements MessageFlyweight {
    
    @Override
    public MessageFlyweight wrapAndApplyHeader(MutableDirectBuffer buffer, int offset, MessageHeaderEncoder headerEncoder) {
        super.wrapAndApplyHeader(buffer, offset, headerEncoder);
        return this;
    }
    
    // All other methods are inherited from OrderEncoder and already implement the required functionality
    // sbeTemplateId(), encodedLength(), sbeBlockLength(), sbeSchemaId(), sbeSchemaVersion() are all available
}
