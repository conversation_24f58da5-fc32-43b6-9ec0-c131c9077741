package org.example.messaging;

import io.aeron.Publication;
import org.agrona.concurrent.UnsafeBuffer;
import org.agrona.sbe.MessageEncoderFlyweight;
import org.example.sbe.MessageHeaderEncoder;

import java.nio.ByteBuffer;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;

/**
 * Generic SBE message publisher that manages encoder instances efficiently.
 * This class provides a clean API for publishing different types of SBE messages
 * while handling encoder caching, buffer management, and message header encoding automatically.
 * 
 * Thread-safe for concurrent access from multiple NodeApps.
 */
public class SbeMessagePublisher {
    
    private static final int DEFAULT_BUFFER_SIZE = 2048;
    
    // Thread-safe cache of encoder instances
    private final ConcurrentHashMap<Class<?>, MessageEncoderFlyweight> encoderCache = new ConcurrentHashMap<>();
    
    // Shared resources
    private final UnsafeBuffer messageBuffer;
    private final MessageHeaderEncoder messageHeaderEncoder;
    private final Publication publication;
    
    // Lock for thread-safe publishing operations
    private final ReentrantLock publishLock = new ReentrantLock();
    
    // Current encoder being used (for fluent API)
    private volatile MessageEncoderFlyweight currentEncoder;
    
    /**
     * Creates a new SBE message publisher.
     * @param publication the Aeron publication to use for sending messages
     */
    public SbeMessagePublisher(Publication publication) {
        this(publication, DEFAULT_BUFFER_SIZE);
    }
    
    /**
     * Creates a new SBE message publisher with custom buffer size.
     * @param publication the Aeron publication to use for sending messages
     * @param bufferSize the size of the message buffer
     */
    public SbeMessagePublisher(Publication publication, int bufferSize) {
        this.publication = publication;
        this.messageBuffer = new UnsafeBuffer(ByteBuffer.allocate(bufferSize));
        this.messageHeaderEncoder = new MessageHeaderEncoder();
    }
    
    /**
     * Gets an encoder instance for the specified encoder class.
     * Encoders are cached and reused for performance.
     * 
     * @param encoderClass the class of the encoder to get
     * @param <T> the encoder type
     * @return the encoder instance ready for use
     * @throws RuntimeException if the encoder cannot be instantiated
     */
    @SuppressWarnings("unchecked")
    public <T extends MessageEncoderFlyweight> T getEncoder(Class<T> encoderClass) {
        MessageEncoderFlyweight encoder = encoderCache.computeIfAbsent(encoderClass, clazz -> {
            try {
                return (MessageEncoderFlyweight) clazz.getDeclaredConstructor().newInstance();
            } catch (Exception e) {
                throw new RuntimeException("Failed to instantiate encoder: " + clazz.getSimpleName(), e);
            }
        });
        
        // Cast is safe because we store the correct type in the cache
        T typedEncoder = (T) encoder;
        
        // Set as current encoder for fluent API
        this.currentEncoder = typedEncoder;
        
        // Wrap the encoder with the shared buffer and header
        publishLock.lock();
        try {
            if (typedEncoder instanceof MessageFlyweight) {
                ((MessageFlyweight) typedEncoder).wrapAndApplyHeader(messageBuffer, 0, messageHeaderEncoder);
            } else {
                // Fallback for encoders that don't implement MessageFlyweight
                typedEncoder.wrapAndApplyHeader(messageBuffer, 0, messageHeaderEncoder);
            }
        } finally {
            publishLock.unlock();
        }
        
        return typedEncoder;
    }
    
    /**
     * Publishes the currently configured encoder's message.
     * This method should be called after populating the encoder with data.
     * 
     * @return the result of the publish operation
     */
    public PublishResult publish() {
        if (currentEncoder == null) {
            return PublishResult.failure("No encoder configured. Call getEncoder() first.");
        }
        
        return publish(currentEncoder);
    }
    
    /**
     * Publishes a specific encoder's message.
     * 
     * @param encoder the encoder containing the message to publish
     * @return the result of the publish operation
     */
    public PublishResult publish(MessageEncoderFlyweight encoder) {
        if (!publication.isConnected()) {
            return PublishResult.notConnected();
        }
        
        publishLock.lock();
        try {
            int totalLength = MessageHeaderEncoder.ENCODED_LENGTH + encoder.encodedLength();
            long result = publication.offer(messageBuffer, 0, totalLength);
            
            if (result > 0) {
                return PublishResult.success(result);
            } else if (result == Publication.BACK_PRESSURED) {
                return PublishResult.backpressure();
            } else if (result == Publication.NOT_CONNECTED) {
                return PublishResult.notConnected();
            } else {
                return PublishResult.failure("Publication failed with result: " + result);
            }
        } finally {
            publishLock.unlock();
        }
    }
    
    /**
     * Checks if the publication is connected.
     * @return true if connected, false otherwise
     */
    public boolean isConnected() {
        return publication.isConnected();
    }
    
    /**
     * Gets the underlying publication.
     * @return the Aeron publication
     */
    public Publication getPublication() {
        return publication;
    }
    
    /**
     * Clears the encoder cache. Useful for testing or memory management.
     */
    public void clearCache() {
        encoderCache.clear();
        currentEncoder = null;
    }
    
    /**
     * Gets the number of cached encoders.
     * @return the cache size
     */
    public int getCacheSize() {
        return encoderCache.size();
    }
}
