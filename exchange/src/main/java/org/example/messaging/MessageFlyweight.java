package org.example.messaging;

import org.agrona.MutableDirectBuffer;
import org.example.sbe.MessageHeaderEncoder;

/**
 * Interface that all SBE message encoders should implement for polymorphic message handling.
 * This interface provides a standardized way to work with different message types.
 */
public interface MessageFlyweight {
    
    /**
     * Gets the template ID for this message type.
     * @return the SBE template ID
     */
    int sbeTemplateId();
    
    /**
     * Gets the encoded length of the message.
     * @return the length in bytes of the encoded message
     */
    int encodedLength();
    
    /**
     * Wraps the message around a buffer and applies the message header.
     * @param buffer the buffer to wrap
     * @param offset the offset in the buffer
     * @param headerEncoder the message header encoder
     * @return this encoder instance for method chaining
     */
    MessageFlyweight wrapAndApplyHeader(MutableDirectBuffer buffer, int offset, MessageHeaderEncoder headerEncoder);
    
    /**
     * Gets the block length for this message type.
     * @return the SBE block length
     */
    int sbeBlockLength();
    
    /**
     * Gets the schema ID for this message type.
     * @return the SBE schema ID
     */
    int sbeSchemaId();
    
    /**
     * Gets the schema version for this message type.
     * @return the SBE schema version
     */
    int sbeSchemaVersion();
}
