package org.example.messaging;

import io.aeron.Aeron;
import io.aeron.Publication;
import org.example.sbe.*;

import static org.example.config.AeronConfig.*;

/**
 * Example demonstrating how to use SbeMessagePublisher in a NodeApp.
 * This shows the clean API and simplified message publishing workflow.
 */
public class MessagePublisherExample {
    
    public static void main(String[] args) {
        // Setup Aeron (normally done in NodeApp.onStart())
        Aeron aeron = Aeron.connect(new Aeron.Context().aeronDirectoryName(MEDIA_DRIVER_DIRECTORY));
        Publication publication = aeron.addPublication(IPC_CHANNEL, COMMAND_STREAM_ID);
        
        // Create the message publisher
        SbeMessagePublisher publisher = new SbeMessagePublisher(publication);
        
        // Wait for connection
        while (!publisher.isConnected()) {
            Thread.yield();
        }
        
        // Example 1: Publishing an Order message
        System.out.println("Publishing Order message...");
        PublishResult orderResult = publishOrderMessage(publisher);
        System.out.println("Order publish result: " + orderResult);
        
        // Example 2: Publishing an Execution Report message
        System.out.println("Publishing Execution Report message...");
        PublishResult execResult = publishExecutionReportMessage(publisher);
        System.out.println("Execution Report publish result: " + execResult);
        
        // Example 3: Publishing multiple messages efficiently
        System.out.println("Publishing multiple messages...");
        publishMultipleMessages(publisher);
        
        // Cleanup
        aeron.close();
        System.out.println("Example completed. Cache size: " + publisher.getCacheSize());
    }
    
    /**
     * Example of publishing an Order message using the fluent API.
     */
    private static PublishResult publishOrderMessage(SbeMessagePublisher publisher) {
        // Get encoder and populate data in a fluent manner
        publisher.getEncoder(OrderEncoderAdapter.class)
                .orderType(OrderType.LIMIT)
                .accountMsb(1L)
                .accountLsb(2L)
                .quantity(1000L)
                .price(50L);

        // Publish the configured message
        return publisher.publish();
    }
    
    /**
     * Example of publishing an Execution Report message.
     */
    private static PublishResult publishExecutionReportMessage(SbeMessagePublisher publisher) {
        // Get encoder and populate with execution report data
        ExecutionReportEncoderAdapter execEncoder = publisher.getEncoder(ExecutionReportEncoderAdapter.class);
        
        execEncoder.executionId(1L)
                  .orderId(2L)
                  .executionType(ExecutionType.PARTIAL_FILL)
                  .executionStatus(ExecutionStatus.NULL_VAL)
                  .timestamp(System.nanoTime())
                  .symbol("TESTINST")
                  .side(Side.BUY)
                  .totalQuantity(200L)
                  .executedQuantity(100L)
                  .remainingQuantity(100L)
                  .avgPrice(1000L);
        
        // Add trades group
        ExecutionReportEncoderAdapter.TradesEncoder tradesEncoder = execEncoder.tradesCount(2);
        for (int i = 0; i < 2; i++) {
            tradesEncoder.next()
                    .tradeId(123L + i)
                    .tradePrice(100L + i)
                    .tradeQuantity(50L)
                    .tradeTimestamp(System.nanoTime())
                    .counterpartyId(999L);
        }
        
        // Publish the message
        return publisher.publish();
    }
    
    /**
     * Example of publishing multiple messages efficiently using cached encoders.
     */
    private static void publishMultipleMessages(SbeMessagePublisher publisher) {
        // The encoders are cached, so subsequent calls are very efficient
        for (int i = 0; i < 5; i++) {
            // Order messages
            publisher.getEncoder(OrderEncoderAdapter.class)
                    .orderType(OrderType.LIMIT)
                    .accountMsb(1L)
                    .accountLsb(2L)
                    .quantity(1000L + i)
                    .price(50L + i);

            PublishResult orderResult = publisher.publish();
            System.out.println("Order " + i + " result: " + orderResult);

            // Execution Report messages
            publisher.getEncoder(ExecutionReportEncoderAdapter.class)
                    .executionId(100L + i)
                    .orderId(200L + i)
                    .executionType(ExecutionType.FILL)
                    .timestamp(System.nanoTime())
                    .symbol("STOCK" + i)
                    .side(Side.BUY)
                    .totalQuantity(100L)
                    .executedQuantity(100L)
                    .remainingQuantity(0L)
                    .avgPrice(1000L + i);

            PublishResult execResult = publisher.publish();
            System.out.println("Execution Report " + i + " result: " + execResult);
        }
    }
}
