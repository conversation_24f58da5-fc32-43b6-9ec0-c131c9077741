# SBE Message Publisher

A generic message publisher utility that manages SBE message encoders efficiently to simplify message publishing across different NodeApp implementations.

## Features

- **Encoder Caching**: Automatically caches and reuses encoder instances to avoid object creation overhead
- **Clean API**: Simple, fluent interface for getting encoders and publishing messages
- **Thread-Safe**: Safe for concurrent access from multiple NodeApps
- **Automatic Header Management**: Handles message header encoding and buffer management automatically
- **Polymorphic Support**: Works with any SBE encoder through the MessageFlyweight interface
- **Error Handling**: Comprehensive error reporting with PublishResult

## Quick Start

### 1. Create a Publisher

```java
// In your NodeApp's onStart() method
Publication publication = aeron.addPublication(IPC_CHANNEL, COMMAND_STREAM_ID);
SbeMessagePublisher publisher = new SbeMessagePublisher(publication);
```

### 2. Publish Messages

```java
// Order message
publisher.getEncoder(OrderEncoderAdapter.class)
    .orderType(OrderType.LIMIT)
    .accountMsb(1L)
    .accountLsb(2L)
    .quantity(1000L)
    .price(50L);

PublishResult result = publisher.publish();
if (result.isSuccess()) {
    System.out.println("Published at position: " + result.getPosition());
} else {
    System.err.println("Publish failed: " + result.getErrorMessage());
}
```

### 3. Execution Report with Groups

```java
// Execution report with trades
ExecutionReportEncoderAdapter execEncoder = publisher.getEncoder(ExecutionReportEncoderAdapter.class);
execEncoder.executionId(1L)
          .orderId(2L)
          .executionType(ExecutionType.PARTIAL_FILL)
          .symbol("TESTINST")
          .side(Side.BUY)
          .totalQuantity(200L)
          .executedQuantity(100L);

// Add trades group
ExecutionReportEncoderAdapter.TradesEncoder tradesEncoder = execEncoder.tradesCount(2);
for (int i = 0; i < 2; i++) {
    tradesEncoder.next()
            .tradeId(123L + i)
            .tradePrice(100L + i)
            .tradeQuantity(50L);
}

PublishResult result = publisher.publish();
```

## Architecture

### Core Components

1. **SbeMessagePublisher**: Main publisher class that manages encoders and publishing
2. **MessageFlyweight**: Interface that SBE encoders implement for polymorphic handling
3. **PublishResult**: Result object containing success status, position, or error details
4. **Encoder Adapters**: Wrapper classes that make generated SBE encoders implement MessageFlyweight

### Class Hierarchy

```
MessageFlyweight (interface)
    ↑
OrderEncoderAdapter extends OrderEncoder
ExecutionReportEncoderAdapter extends ExecutionReportEncoder
    ↑
SbeMessagePublisher manages all encoders
```

## Benefits Over Manual Encoder Management

### Before (Manual Management)
```java
public class PositionNodeApp {
    private final OrderEncoder orderEncoder = new OrderEncoder();
    private final ExecutionReportEncoder executionReportEncoder = new ExecutionReportEncoder();
    private final MessageHeaderEncoder messageHeaderEncoder = new MessageHeaderEncoder();
    private final UnsafeBuffer unsafeBuffer = new UnsafeBuffer(ByteBuffer.allocate(2048));
    
    private int publishOrderEvent() {
        orderEncoder.wrapAndApplyHeader(unsafeBuffer, 0, messageHeaderEncoder);
        orderEncoder.orderType(OrderType.LIMIT);
        // ... populate fields
        long result = commandStreamPublication.offer(unsafeBuffer, 0, 
            MessageHeaderEncoder.ENCODED_LENGTH + orderEncoder.encodedLength());
        return result < 0 ? 0 : 1;
    }
}
```

### After (Using SbeMessagePublisher)
```java
public class PositionNodeApp {
    private SbeMessagePublisher publisher;
    
    @Override
    public void onStart() {
        // ... setup aeron
        this.publisher = new SbeMessagePublisher(commandStreamPublication);
    }
    
    private PublishResult publishOrderEvent() {
        publisher.getEncoder(OrderEncoderAdapter.class)
            .orderType(OrderType.LIMIT)
            .accountMsb(1L)
            .quantity(quantity++);
        
        return publisher.publish();
    }
}
```

## Performance Characteristics

- **Encoder Reuse**: Encoders are cached and reused, eliminating object creation overhead
- **Thread-Safe Caching**: ConcurrentHashMap ensures safe concurrent access
- **Minimal Locking**: Publishing operations use fine-grained locking only when necessary
- **Memory Efficient**: Single shared buffer for all message types

## Error Handling

The publisher returns detailed error information through PublishResult:

```java
PublishResult result = publisher.publish();

if (result.isSuccess()) {
    long position = result.getPosition();
    // Handle success
} else {
    String error = result.getErrorMessage();
    // Handle specific errors:
    // - "Publication back pressure"
    // - "Publication not connected"
    // - "No encoder configured"
}
```

## Thread Safety

- **Publisher Instance**: Thread-safe for concurrent access
- **Encoder Instances**: Not thread-safe; each thread should get its own encoder instance
- **Caching**: Thread-safe encoder caching using ConcurrentHashMap
- **Publishing**: Synchronized to ensure atomic publish operations

## Testing

Comprehensive test suite included:
- Encoder caching verification
- Message publishing scenarios
- Error condition handling
- Thread safety validation

Run tests with:
```bash
./gradlew test --tests "*SbeMessagePublisherTest*"
```

## Integration with NodeApps

The publisher integrates seamlessly with the NodeApp architecture:

1. Create publisher in `onStart()`
2. Use in `doWork()` methods
3. No cleanup required (Aeron handles publication lifecycle)

This design eliminates the complexity of managing multiple encoder instances while maintaining high performance through efficient caching and reuse.
