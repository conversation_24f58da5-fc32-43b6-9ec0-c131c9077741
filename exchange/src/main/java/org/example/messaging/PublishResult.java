package org.example.messaging;

/**
 * Result of a message publishing operation.
 */
public class PublishResult {
    private final long position;
    private final boolean success;
    private final String errorMessage;
    
    private PublishResult(long position, boolean success, String errorMessage) {
        this.position = position;
        this.success = success;
        this.errorMessage = errorMessage;
    }
    
    /**
     * Creates a successful publish result.
     * @param position the position returned by the publication
     * @return a successful PublishResult
     */
    public static PublishResult success(long position) {
        return new PublishResult(position, true, null);
    }
    
    /**
     * Creates a failed publish result.
     * @param errorMessage the error message
     * @return a failed PublishResult
     */
    public static PublishResult failure(String errorMessage) {
        return new PublishResult(-1, false, errorMessage);
    }
    
    /**
     * Creates a backpressure result (publication buffer full).
     * @return a backpressure PublishResult
     */
    public static PublishResult backpressure() {
        return new PublishResult(-2, false, "Publication back pressure");
    }
    
    /**
     * Creates a not connected result.
     * @return a not connected PublishResult
     */
    public static PublishResult notConnected() {
        return new PublishResult(-3, false, "Publication not connected");
    }
    
    /**
     * Gets the position returned by the publication.
     * @return the position, or negative value if failed
     */
    public long getPosition() {
        return position;
    }
    
    /**
     * Checks if the publish operation was successful.
     * @return true if successful, false otherwise
     */
    public boolean isSuccess() {
        return success;
    }
    
    /**
     * Gets the error message if the operation failed.
     * @return the error message, or null if successful
     */
    public String getErrorMessage() {
        return errorMessage;
    }
    
    @Override
    public String toString() {
        if (success) {
            return "PublishResult{success=true, position=" + position + "}";
        } else {
            return "PublishResult{success=false, position=" + position + ", error='" + errorMessage + "'}";
        }
    }
}
