package org.example.tutorial;


import io.aeron.*;
import io.aeron.archive.Archive;
import io.aeron.archive.ArchivingMediaDriver;
import io.aeron.archive.client.AeronArchive;
import io.aeron.archive.client.RecordingDescriptorConsumer;
import io.aeron.archive.codecs.SourceLocation;
import io.aeron.driver.MediaDriver;
import io.aeron.logbuffer.Header;
import io.aeron.samples.archive.RecordingDescriptor;
import io.aeron.samples.archive.RecordingDescriptorCollector;
import org.agrona.CloseHelper;
import org.agrona.DirectBuffer;
import org.agrona.ExpandableArrayBuffer;
import org.agrona.collections.MutableLong;
import org.agrona.concurrent.IdleStrategy;
import org.agrona.concurrent.SleepingIdleStrategy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;

import static org.example.config.AeronConfig.ARCHIVE_DIRECTORY;

public class AeronArchiveTest {

    public static final String REPLICATION_CHANNEL = "aeron:udp?endpoint=localhost:0";
    public static final String CONTROL_REQUEST_CHANNEL = "aeron:udp?endpoint=localhost:8010";
    public static final String CONTROL_RESPONSE_CHANNEL = "aeron:udp?endpoint=localhost:0";
    private static final Logger LOGGER = LoggerFactory.getLogger(AeronArchiveTest.class);
    private final String channel = "aeron:ipc";
    private final int streamCapture = 16;
    private final int streamReplay = 17;
    private final int sendCount = 0;
    private final int readCount = 200_000;

    private final IdleStrategy idleStrategy = new SleepingIdleStrategy();
    private final ExpandableArrayBuffer buffer = new ExpandableArrayBuffer();
    private final File tempDir = new File(ARCHIVE_DIRECTORY);
    boolean complete = false;
    private AeronArchive aeronArchive;
    private Aeron aeron;
    private ArchivingMediaDriver mediaDriver;
    final RecordingDescriptorCollector recordingDescriptorCollector = new RecordingDescriptorCollector(10);

    public static void main(final String[] args) {
        final AeronArchiveTest simplestCase = new AeronArchiveTest();
        try {
            simplestCase.setup();
            LOGGER.info("Writing");
            simplestCase.write();
            LOGGER.info("Reading");
            simplestCase.read();

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            simplestCase.cleanUp();
        }
    }

    private void cleanUp() {
        CloseHelper.quietClose(aeronArchive);
        CloseHelper.quietClose(aeron);
        CloseHelper.quietClose(mediaDriver);
    }

    private void read() {
        try (AeronArchive reader = AeronArchive.connect(new AeronArchive.Context()
                .controlRequestChannel(CONTROL_REQUEST_CHANNEL)
                .controlResponseChannel(CONTROL_RESPONSE_CHANNEL)
                .aeron(aeron))) {
            final long recordingId = findLatestRecording(reader, channel, streamCapture);
            final long position = AeronArchive.NULL_POSITION;
            final long length = Long.MAX_VALUE;

            final long sessionId = reader.startReplay(recordingId, position, length, channel, streamReplay);
            final String channelRead = ChannelUri.addSessionId(channel, (int) sessionId);

            final Subscription subscription = reader.context().aeron().addSubscription(channelRead, streamReplay);

            while (!subscription.isConnected()) {
                idleStrategy.idle();
            }

            while (!complete) {
                final int fragments = subscription.poll(this::archiveReader, 1);
                idleStrategy.idle(fragments);
            }
        }
    }


    private void write() {

        RecordingDescriptor recodingDescriptor = null;
        findLatestRecording(aeronArchive, channel, streamCapture);
        if (recordingDescriptorCollector.descriptors().isEmpty()) {
            aeronArchive.startRecording(channel, streamCapture, SourceLocation.LOCAL);
        } else {
            recodingDescriptor = recordingDescriptorCollector.descriptors().getFirst();
            aeronArchive.extendRecording(recodingDescriptor.recordingId(), channel, streamCapture, SourceLocation.LOCAL);
        }

        try (Publication publication = createPublication(recodingDescriptor)) {
            while (!publication.isConnected()) {
                idleStrategy.idle();
            }

            for (int i = 0; i <= sendCount; i++) {
                buffer.putInt(0, i);
                while (publication.offer(buffer, 0, Integer.BYTES) < 0) {
                    idleStrategy.idle();
                }
            }

        }
    }

    private Publication createPublication(RecordingDescriptor recodingDescriptor) {
        if (recodingDescriptor == null) {
            return aeron.addExclusivePublication(channel, streamCapture);
        }

        final String publicationExtendChannel = new ChannelUriStringBuilder()
                .media("ipc")
                .initialPosition(recodingDescriptor.stopPosition(), recodingDescriptor.initialTermId(), recodingDescriptor.termBufferLength())
                .mtu(recodingDescriptor.mtuLength())
                .build();

        return aeron.addExclusivePublication(publicationExtendChannel, streamCapture);
    }

    private long findLatestRecording(final AeronArchive archive, final String channel, final int stream, long fromRecordingId) {
        final MutableLong lastRecordingId = new MutableLong();

        final RecordingDescriptorConsumer consumer =
                (controlSessionId, correlationId, recordingId,
                 startTimestamp, stopTimestamp, startPosition,
                 stopPosition, initialTermId, segmentFileLength,
                 termBufferLength, mtuLength, sessionId,
                 streamId, strippedChannel, originalChannel,
                 sourceIdentity) -> lastRecordingId.set(recordingId);


        final int recordCount = 100;

        final int foundCount = archive.listRecordingsForUri(fromRecordingId, recordCount, channel, stream, recordingDescriptorCollector.reset());

        if (foundCount == 0) {
            return -1;
        }

        return lastRecordingId.get();
    }

    private long findLatestRecording(final AeronArchive archive, final String channel, final int stream) {
        final MutableLong lastRecordingId = new MutableLong();

        final RecordingDescriptorConsumer consumer =
                (controlSessionId, correlationId, recordingId,
                 startTimestamp, stopTimestamp, startPosition,
                 stopPosition, initialTermId, segmentFileLength,
                 termBufferLength, mtuLength, sessionId,
                 streamId, strippedChannel, originalChannel,
                 sourceIdentity) -> lastRecordingId.set(recordingId);

        final long fromRecordingId = 0L;
        final int recordCount = 100;

        final int foundCount = archive.listRecordingsForUri(fromRecordingId, recordCount, channel, stream, recordingDescriptorCollector.reset());

//        if (foundCount == 0) {
//            throw new IllegalStateException("no recordings found");
//        }

        return lastRecordingId.get();
    }

    int count = 0;
    int targetNumber = 123;
    int targetCount = 0;

    public void archiveReader(final DirectBuffer buffer, final int offset, final int length, final Header header) {
        final int valueRead = buffer.getInt(offset);
        LOGGER.info("Received {}", valueRead);
        if (valueRead == targetNumber) {
            targetCount++;
        }
        if (count == readCount) {
            System.out.println("total count of the read is " + count);
            System.out.println("the total target number count is " + targetCount);
            complete = true;
        }
        count++;
    }

    public void setup() {
        mediaDriver = ArchivingMediaDriver.launch(
                new MediaDriver.Context()
                        .spiesSimulateConnection(true)
                        .dirDeleteOnStart(true),
                new Archive.Context()
                        .deleteArchiveOnStart(false)
                        .controlChannel(CONTROL_REQUEST_CHANNEL)
                        .replicationChannel(REPLICATION_CHANNEL)
                        .archiveDir(tempDir)
        );

        aeron = Aeron.connect();

        aeronArchive = AeronArchive.connect(
                new AeronArchive.Context()
                        .aeron(aeron)
                        .controlRequestChannel(CONTROL_REQUEST_CHANNEL)
                        .controlResponseChannel(CONTROL_RESPONSE_CHANNEL)
        );
    }
}

