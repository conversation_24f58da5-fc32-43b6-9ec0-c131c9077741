package org.example.tutorial;

import java.io.IOException;
import java.io.PrintWriter;
import java.nio.file.Path;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Advanced analysis utilities for parsed Aeron recording data.
 * Provides statistical analysis, filtering, and export capabilities.
 */
public class AeronRecordingAnalyzer {

    /**
     * Statistics for a collection of parsed messages.
     */
    public static class MessageStatistics {
        public final int totalMessages;
        public final int totalDataFrames;
        public final int totalPaddingFrames;
        public final long totalPayloadBytes;
        public final Set<Integer> uniqueSessionIds;
        public final Set<Integer> uniqueStreamIds;
        public final Set<Integer> uniqueTermIds;
        public final Map<String, Integer> messagesByFile;
        public final Map<Integer, Integer> messagesByFrameType;
        public final OptionalInt minPayloadSize;
        public final OptionalInt maxPayloadSize;
        public final OptionalDouble avgPayloadSize;

        public MessageStatistics(List<AeronRecordingFileParser.ParsedMessage> messages) {
            this.totalMessages = messages.size();
            this.totalDataFrames = (int) messages.stream().filter(m -> m.frameType == 1).count();
            this.totalPaddingFrames = (int) messages.stream().filter(m -> m.frameType == 0).count();
            this.totalPayloadBytes = messages.stream().mapToLong(m -> m.payloadLength).sum();

            this.uniqueSessionIds = messages.stream().map(m -> m.sessionId).collect(Collectors.toSet());
            this.uniqueStreamIds = messages.stream().map(m -> m.streamId).collect(Collectors.toSet());
            this.uniqueTermIds = messages.stream().map(m -> m.termId).collect(Collectors.toSet());

            this.messagesByFile = messages.stream()
                    .collect(Collectors.groupingBy(m -> m.fileName, Collectors.summingInt(m -> 1)));

            this.messagesByFrameType = messages.stream()
                    .collect(Collectors.groupingBy(m -> m.frameType, Collectors.summingInt(m -> 1)));

            IntSummaryStatistics payloadStats = messages.stream()
                    .mapToInt(m -> m.payloadLength)
                    .summaryStatistics();

            this.minPayloadSize = payloadStats.getCount() > 0 ?
                    OptionalInt.of(payloadStats.getMin()) : OptionalInt.empty();
            this.maxPayloadSize = payloadStats.getCount() > 0 ?
                    OptionalInt.of(payloadStats.getMax()) : OptionalInt.empty();
            this.avgPayloadSize = payloadStats.getCount() > 0 ?
                    OptionalDouble.of(payloadStats.getAverage()) : OptionalDouble.empty();
        }

        public void printSummary() {
            System.out.println("Message Statistics Summary:");
            System.out.println("==========================");
            System.out.println("Total Messages: " + totalMessages);
            System.out.println("Data Frames: " + totalDataFrames);
            System.out.println("Padding Frames: " + totalPaddingFrames);
            System.out.println("Total Payload Bytes: " + totalPayloadBytes);
            System.out.println("Unique Session IDs: " + uniqueSessionIds.size() + " " + uniqueSessionIds);
            System.out.println("Unique Stream IDs: " + uniqueStreamIds.size() + " " + uniqueStreamIds);
            System.out.println("Unique Term IDs: " + uniqueTermIds.size());

            if (minPayloadSize.isPresent()) {
                System.out.println("Payload Size - Min: " + minPayloadSize.getAsInt() +
                        ", Max: " + maxPayloadSize.getAsInt() +
                        ", Avg: " + String.format("%.2f", avgPayloadSize.getAsDouble()));
            }

            System.out.println("\nMessages by File:");
            messagesByFile.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .forEach(entry -> System.out.println("  " + entry.getKey() + ": " + entry.getValue()));

            System.out.println("\nMessages by Frame Type:");
            messagesByFrameType.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .forEach(entry -> System.out.println("  Type " + entry.getKey() + ": " + entry.getValue()));
        }
    }

    /**
     * Filters for selecting specific messages.
     */
    public static class MessageFilter {
        private Integer sessionId;
        private Integer streamId;
        private Integer frameType;
        private Integer minPayloadSize;
        private Integer maxPayloadSize;
        private String fileName;
        private Long minFileOffset;
        private Long maxFileOffset;

        public MessageFilter sessionId(int sessionId) {
            this.sessionId = sessionId;
            return this;
        }

        public MessageFilter streamId(int streamId) {
            this.streamId = streamId;
            return this;
        }

        public MessageFilter frameType(int frameType) {
            this.frameType = frameType;
            return this;
        }

        public MessageFilter payloadSizeRange(int min, int max) {
            this.minPayloadSize = min;
            this.maxPayloadSize = max;
            return this;
        }

        public MessageFilter fileName(String fileName) {
            this.fileName = fileName;
            return this;
        }

        public MessageFilter fileOffsetRange(long min, long max) {
            this.minFileOffset = min;
            this.maxFileOffset = max;
            return this;
        }

        public boolean matches(AeronRecordingFileParser.ParsedMessage message) {
            if (sessionId != null && message.sessionId != sessionId) return false;
            if (streamId != null && message.streamId != streamId) return false;
            if (frameType != null && message.frameType != frameType) return false;
            if (minPayloadSize != null && message.payloadLength < minPayloadSize) return false;
            if (maxPayloadSize != null && message.payloadLength > maxPayloadSize) return false;
            if (fileName != null && !message.fileName.equals(fileName)) return false;
            if (minFileOffset != null && message.fileOffset < minFileOffset) return false;
            if (maxFileOffset != null && message.fileOffset > maxFileOffset) return false;
            return true;
        }
    }

    /**
     * Analyzes messages and generates comprehensive statistics.
     */
    public static MessageStatistics analyzeMessages(List<AeronRecordingFileParser.ParsedMessage> messages) {
        return new MessageStatistics(messages);
    }

    /**
     * Filters messages based on specified criteria.
     */
    public static List<AeronRecordingFileParser.ParsedMessage> filterMessages(
            List<AeronRecordingFileParser.ParsedMessage> messages, MessageFilter filter) {
        return messages.stream()
                .filter(filter::matches)
                .collect(Collectors.toList());
    }

    /**
     * Groups messages by recording ID (extracted from filename).
     */
    public static Map<Long, List<AeronRecordingFileParser.ParsedMessage>> groupByRecordingId(
            List<AeronRecordingFileParser.ParsedMessage> messages) {
        return messages.stream()
                .collect(Collectors.groupingBy(msg -> extractRecordingIdFromFileName(msg.fileName)));
    }

    /**
     * Groups messages by stream ID.
     */
    public static Map<Integer, List<AeronRecordingFileParser.ParsedMessage>> groupByStreamId(
            List<AeronRecordingFileParser.ParsedMessage> messages) {
        return messages.stream()
                .collect(Collectors.groupingBy(msg -> msg.streamId));
    }

    /**
     * Exports messages to CSV format.
     */
    public static void exportToCsv(List<AeronRecordingFileParser.ParsedMessage> messages, String fileName)
            throws IOException {
        try (PrintWriter writer = new PrintWriter(fileName)) {
            // CSV header
            writer.println("fileName,fileOffset,payloadOffset,frameLength,payloadLength,frameType," +
                    "flags,sessionId,streamId,termId,termOffset,reservedValue,flagsDescription");

            // CSV data
            for (AeronRecordingFileParser.ParsedMessage msg : messages) {
                writer.printf("%s,%d,%d,%d,%d,%d,0x%02X,%d,%d,%d,%d,%d,\"%s\"%n",
                        msg.fileName, msg.fileOffset, msg.payloadOffset, msg.frameLength,
                        msg.payloadLength, msg.frameType, msg.flags, msg.sessionId,
                        msg.streamId, msg.termId, msg.termOffset, msg.reservedValue,
                        msg.getFlagsDescription());
            }
        }
    }

    /**
     * Exports payload data to binary files (one file per message).
     */
    public static void exportPayloads(List<AeronRecordingFileParser.ParsedMessage> messages,
                                      String outputDirectory) throws IOException {
        Path outputPath = Path.of(outputDirectory);
        if (!outputPath.toFile().exists()) {
            outputPath.toFile().mkdirs();
        }

        for (int i = 0; i < messages.size(); i++) {
            AeronRecordingFileParser.ParsedMessage msg = messages.get(i);
            if (msg.payloadLength > 0) {
                String payloadFileName = String.format("payload_%04d_%s_offset_%d.bin",
                        i, msg.fileName.replace(".rec", ""), msg.payloadOffset);
                Path payloadFile = outputPath.resolve(payloadFileName);
                java.nio.file.Files.write(payloadFile, msg.payloadData);
            }
        }
    }

    /**
     * Finds messages with specific payload patterns (hex search).
     */
    public static List<AeronRecordingFileParser.ParsedMessage> findMessagesWithPayloadPattern(
            List<AeronRecordingFileParser.ParsedMessage> messages, String hexPattern) {
        byte[] pattern = hexStringToByteArray(hexPattern.replaceAll("\\s+", ""));

        return messages.stream()
                .filter(msg -> containsPattern(msg.payloadData, pattern))
                .collect(Collectors.toList());
    }

    /**
     * Generates a detailed report of all messages.
     */
    public static void generateDetailedReport(List<AeronRecordingFileParser.ParsedMessage> messages,
                                              String reportFileName) throws IOException {
        try (PrintWriter writer = new PrintWriter(reportFileName)) {
            writer.println("Aeron Recording File Analysis Report");
            writer.println("===================================");
            writer.println("Generated: " + new Date());
            writer.println();

            MessageStatistics stats = analyzeMessages(messages);

            writer.println("SUMMARY STATISTICS");
            writer.println("------------------");
            writer.println("Total Messages: " + stats.totalMessages);
            writer.println("Data Frames: " + stats.totalDataFrames);
            writer.println("Padding Frames: " + stats.totalPaddingFrames);
            writer.println("Total Payload Bytes: " + stats.totalPayloadBytes);
            writer.println("Unique Session IDs: " + stats.uniqueSessionIds);
            writer.println("Unique Stream IDs: " + stats.uniqueStreamIds);
            writer.println();

            writer.println("DETAILED MESSAGE LIST");
            writer.println("--------------------");
            for (int i = 0; i < messages.size(); i++) {
                AeronRecordingFileParser.ParsedMessage msg = messages.get(i);
                writer.println("Message " + (i + 1) + ":");
                writer.println("  File: " + msg.fileName);
                writer.println("  File Offset: " + msg.fileOffset);
                writer.println("  Payload Offset: " + msg.payloadOffset);
                writer.println("  Frame Length: " + msg.frameLength);
                writer.println("  Payload Length: " + msg.payloadLength);
                writer.println("  Frame Type: " + msg.frameType);
                writer.println("  Flags: 0x" + String.format("%02X", msg.flags) + " (" + msg.getFlagsDescription() + ")");
                writer.println("  Session ID: " + msg.sessionId);
                writer.println("  Stream ID: " + msg.streamId);
                writer.println("  Term ID: " + msg.termId);
                writer.println("  Term Offset: " + msg.termOffset);
                writer.println("  Reserved Value: " + msg.reservedValue);
                if (msg.payloadLength > 0) {
                    writer.println("  Payload Preview: " + formatHexDump(msg.payloadData, 32));
                }
                writer.println();
            }
        }
    }

    // Helper methods

    private static long extractRecordingIdFromFileName(String fileName) {
        int dashIndex = fileName.indexOf('-');
        if (dashIndex > 0) {
            return Long.parseLong(fileName.substring(0, dashIndex));
        }
        return 0;
    }

    private static byte[] hexStringToByteArray(String hex) {
        int len = hex.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hex.charAt(i), 16) << 4)
                    + Character.digit(hex.charAt(i + 1), 16));
        }
        return data;
    }

    private static boolean containsPattern(byte[] data, byte[] pattern) {
        if (pattern.length == 0 || data.length < pattern.length) {
            return false;
        }

        for (int i = 0; i <= data.length - pattern.length; i++) {
            boolean found = true;
            for (int j = 0; j < pattern.length; j++) {
                if (data[i + j] != pattern[j]) {
                    found = false;
                    break;
                }
            }
            if (found) {
                return true;
            }
        }
        return false;
    }

    private static String formatHexDump(byte[] data, int maxBytes) {
        StringBuilder sb = new StringBuilder();
        int limit = Math.min(data.length, maxBytes);

        for (int i = 0; i < limit; i++) {
            if (i > 0) sb.append(" ");
            sb.append(String.format("%02X", data[i] & 0xFF));
        }

        if (data.length > maxBytes) {
            sb.append(" ... (").append(data.length - maxBytes).append(" more bytes)");
        }

        return sb.toString();
    }

    /**
     * Example usage demonstrating advanced analysis features.
     */
    public static void main(String[] args) throws IOException {

        Path directoryPath = Path.of("/Users/<USER>/core/archive");

        // Parse all messages
        List<AeronRecordingFileParser.ParsedMessage> allMessages =
                AeronRecordingFileParser.parseAllRecordingFiles(directoryPath);

        // Generate statistics
        MessageStatistics stats = analyzeMessages(allMessages);
        stats.printSummary();

        // Example filtering: Find all data frames with payload > 0
        List<AeronRecordingFileParser.ParsedMessage> dataMessages = filterMessages(allMessages,
                new MessageFilter().frameType(1).payloadSizeRange(1, Integer.MAX_VALUE));

        System.out.println("\nData messages with payload: " + dataMessages.size());

        // Export to CSV
        exportToCsv(allMessages, "recording_analysis.csv");
        System.out.println("Exported to recording_analysis.csv");

        // Generate detailed report
        generateDetailedReport(allMessages, "recording_report.txt");
        System.out.println("Generated detailed report: recording_report.txt");
    }
}
