package org.example.tutorial;

import java.io.IOException;
import java.nio.file.Path;
import java.util.List;

/**
 * Example demonstrating how to use the Aeron Recording File Parser
 * to extract message data with precise offset information.
 */
public class RecordingParserExample {

    public static void main(String[] args) throws IOException {
        // Example directory path - replace with your actual directory
        String directoryPath = "/Users/<USER>/core/archive";

        System.out.println("Aeron Recording File Parser Example");
        System.out.println("===================================");
        System.out.println("Directory: " + directoryPath);
        System.out.println();

        try {
            // Step 1: Scan directory for recording files
            List<AeronRecordingFileParser.SegmentFileInfo> segmentFiles =
                    AeronRecordingFileParser.scanRecordingFiles(Path.of(directoryPath));

            System.out.println("Found " + segmentFiles.size() + " recording segment files:");
            for (AeronRecordingFileParser.SegmentFileInfo segmentFile : segmentFiles) {
                System.out.println("  " + segmentFile);
            }
            System.out.println();

            // Step 2: Parse all files and extract messages
            List<AeronRecordingFileParser.ParsedMessage> allMessages =
                    AeronRecordingFileParser.parseAllRecordingFiles(Path.of(directoryPath));

            System.out.println("Total messages extracted: " + allMessages.size());
            System.out.println();

            // Step 3: Display detailed information for each message
            for (int i = 0; i < Math.min(allMessages.size(), 10); i++) { // Show first 10 messages
                AeronRecordingFileParser.ParsedMessage msg = allMessages.get(i);

                System.out.println("Message " + (i + 1) + ":");
                System.out.println("  File: " + msg.fileName);
                System.out.println("  Frame starts at file offset: " + msg.fileOffset);
                System.out.println("  Payload starts at file offset: " + msg.payloadOffset);
                System.out.println("  Frame length (total): " + msg.frameLength + " bytes");
                System.out.println("  Payload length: " + msg.payloadLength + " bytes");
                System.out.println("  Frame type: " + getFrameTypeName(msg.frameType));
                System.out.println("  Flags: 0x" + String.format("%02X", msg.flags) + " (" + msg.getFlagsDescription() + ")");
                System.out.println("  Session ID: " + msg.sessionId);
                System.out.println("  Stream ID: " + msg.streamId);
                System.out.println("  Term ID: " + msg.termId);
                System.out.println("  Term Offset: " + msg.termOffset);
                System.out.println("  Reserved Value: " + msg.reservedValue);

                if (msg.payloadLength > 0) {
                    System.out.println("  Payload preview (hex): " + formatPayloadHex(msg.payloadData, 32));
                    System.out.println("  Payload preview (ASCII): " + formatPayloadAscii(msg.payloadData, 32));
                }
                System.out.println();
            }

            if (allMessages.size() > 10) {
                System.out.println("... and " + (allMessages.size() - 10) + " more messages");
                System.out.println();
            }

            // Step 4: Generate analysis
            AeronRecordingAnalyzer.MessageStatistics stats =
                    AeronRecordingAnalyzer.analyzeMessages(allMessages);
            stats.printSummary();

            // Step 5: Example filtering - find messages with specific characteristics
            System.out.println("\nFiltering Examples:");
            System.out.println("==================");

            // Find all data frames (type 1)
            List<AeronRecordingFileParser.ParsedMessage> dataFrames =
                    AeronRecordingAnalyzer.filterMessages(allMessages,
                            new AeronRecordingAnalyzer.MessageFilter().frameType(1));
            System.out.println("Data frames: " + dataFrames.size());

            // Find messages with payload > 0
            List<AeronRecordingFileParser.ParsedMessage> messagesWithPayload =
                    AeronRecordingAnalyzer.filterMessages(allMessages,
                            new AeronRecordingAnalyzer.MessageFilter().payloadSizeRange(1, Integer.MAX_VALUE));
            System.out.println("Messages with payload: " + messagesWithPayload.size());

            // Step 6: Demonstrate precise offset access
            if (!messagesWithPayload.isEmpty()) {
                System.out.println("\nPrecise Offset Access Example:");
                System.out.println("==============================");

                AeronRecordingFileParser.ParsedMessage firstMessage = messagesWithPayload.get(0);
                System.out.println("First message with payload:");
                System.out.println("  File: " + firstMessage.fileName);
                System.out.println("  Frame header starts at byte: " + firstMessage.fileOffset);
                System.out.println("  Message payload starts at byte: " + firstMessage.payloadOffset);
                System.out.println("  Payload length: " + firstMessage.payloadLength + " bytes");
                System.out.println("  To read this payload from file:");
                System.out.println("    - Seek to offset: " + firstMessage.payloadOffset);
                System.out.println("    - Read " + firstMessage.payloadLength + " bytes");
                System.out.println("  Full payload (hex): " + formatPayloadHex(firstMessage.payloadData, firstMessage.payloadLength));
            }

        } catch (IOException e) {
            System.err.println("Error processing recording files: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static String getFrameTypeName(int frameType) {
        switch (frameType) {
            case 0:
                return "PADDING";
            case 1:
                return "DATA";
            case 2:
                return "NAK";
            case 3:
                return "STATUS_MESSAGE";
            case 4:
                return "ERROR";
            case 5:
                return "SETUP";
            case 6:
                return "RTTM";
            case 7:
                return "RESOLUTION";
            default:
                return "UNKNOWN(" + frameType + ")";
        }
    }

    private static String formatPayloadHex(byte[] data, int maxBytes) {
        if (data.length == 0) {
            return "(empty)";
        }

        StringBuilder sb = new StringBuilder();
        int limit = Math.min(data.length, maxBytes);

        for (int i = 0; i < limit; i++) {
            if (i > 0) sb.append(" ");
            sb.append(String.format("%02X", data[i] & 0xFF));
        }

        if (data.length > maxBytes) {
            sb.append(" ... (").append(data.length - maxBytes).append(" more bytes)");
        }

        return sb.toString();
    }

    private static String formatPayloadAscii(byte[] data, int maxBytes) {
        if (data.length == 0) {
            return "(empty)";
        }

        StringBuilder sb = new StringBuilder();
        int limit = Math.min(data.length, maxBytes);

        for (int i = 0; i < limit; i++) {
            byte b = data[i];
            if (b >= 32 && b <= 126) { // Printable ASCII
                sb.append((char) b);
            } else {
                sb.append('.');
            }
        }

        if (data.length > maxBytes) {
            sb.append(" ... (").append(data.length - maxBytes).append(" more bytes)");
        }

        return sb.toString();
    }
}
