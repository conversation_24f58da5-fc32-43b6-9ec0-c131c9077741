package org.example.services;

import io.aeron.Aeron;
import io.aeron.archive.client.AeronArchive;
import io.aeron.archive.codecs.SourceLocation;
import org.agrona.concurrent.Agent;
import org.example.models.NodeAppStatus;

import static org.example.config.AeronConfig.*;
import static org.example.models.NodeAppStatus.INIT;
import static org.example.models.NodeAppStatus.RUNNING;

public class ArchiveNodeApp implements Agent {

    NodeAppStatus nodeAppStatus = INIT;
    Aeron aeron;
    AeronArchive aeronArchive;

    @Override
    public void onStart() {
        nodeAppStatus = INIT;
        this.aeron = Aeron.connect(new Aeron.Context().aeronDirectoryName(MEDIA_DRIVER_DIRECTORY));
        this.aeronArchive = AeronArchive.connect(
                new AeronArchive.Context()
                        .aeron(aeron)
                        .controlRequestChannel(CONTROL_REQUEST_CHANNEL)
                        .controlResponseChannel(CONTROL_RESPONSE_CHANNEL)
        );
    }


    @Override
    public int doWork() {
        switch (nodeAppStatus) {
            case INIT:
                startRecording();
                break;
            case RUNNING:
                break;
        }
        return 0;
    }

    private void startRecording() {
        nodeAppStatus = RUNNING;
        aeronArchive.startRecording(IPC_CHANNEL, EVENT_STREAM_ID, SourceLocation.LOCAL);
    }

    @Override
    public String roleName() {
        return "ArchiveNodeApp";
    }

}
