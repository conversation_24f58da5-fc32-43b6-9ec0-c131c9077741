package org.example.services;

import io.aeron.Aeron;
import io.aeron.FragmentAssembler;
import io.aeron.Subscription;
import io.aeron.logbuffer.FragmentHandler;
import io.aeron.logbuffer.Header;
import org.agrona.DirectBuffer;
import org.agrona.concurrent.Agent;
import org.example.messaging.*;
import org.example.models.NodeAppStatus;
import org.example.sbe.*;

import static org.example.config.AeronConfig.*;
import static org.example.models.NodeAppStatus.INIT;
import static org.example.models.NodeAppStatus.RUNNING;

/**
 * Refactored PositionNodeApp demonstrating the use of SbeMessagePublisher.
 * This version eliminates the need for manual encoder management and provides
 * a cleaner, more maintainable approach to message publishing.
 */
public class RefactoredPositionNodeApp implements Agent, FragmentHandler {

    private final FragmentAssembler fragmentAssembler = new FragmentAssembler(this);
    private final MessageHeaderDecoder messageHeaderDecoder = new MessageHeaderDecoder();

    // Replaced multiple encoder fields with a single publisher
    private SbeMessagePublisher messagePublisher;
    
    Aeron aeron;
    Subscription eventStreamSubscription;
    NodeAppStatus nodeAppStatus = INIT;

    // Business logic state
    long quantity = 2000L;
    long orderPrice = 1L;
    long tradeId = 1L;
    long tradePrice = 1000L;

    @Override
    public void onStart() {
        this.aeron = Aeron.connect(new Aeron.Context().aeronDirectoryName(MEDIA_DRIVER_DIRECTORY));
        this.eventStreamSubscription = aeron.addSubscription(IPC_CHANNEL, EVENT_STREAM_ID);
        
        // Create the message publisher with the command stream publication
        this.messagePublisher = new SbeMessagePublisher(
            aeron.addPublication(IPC_CHANNEL, COMMAND_STREAM_ID)
        );
    }

    @Override
    public int doWork() {
        switch (nodeAppStatus) {
            case INIT:
                return checkConnectionStatus();
            case RUNNING:
                return publishOrderEvent() + pullSequenceEvent() + publishExecutionReportEvent();
            default:
                return 0;
        }
    }

    private int pullSequenceEvent() {
        return eventStreamSubscription.poll(fragmentAssembler, FRAGMENT_LIMIT);
    }

    /**
     * Simplified order publishing using SbeMessagePublisher.
     * No need to manage buffers, headers, or calculate lengths manually.
     */
    private int publishOrderEvent() {
        // Get encoder and populate with data

        ExecutionReportEncoder encoder = messagePublisher.getEncoder(ExecutionReportEncoder.class);
        messagePublisher.getEncoder(OrderEncoderAdapter.class)
                .orderType(OrderType.LIMIT)
                .accountMsb(1L)
                .accountLsb(2L)
                .quantity(quantity++)
                .price(orderPrice++);

        // Publish and handle result
        PublishResult result = messagePublisher.publish();
        
        if (result.isSuccess()) {
            // Could store position for tracking if needed
            // latestPosition = result.getPosition();
            return 1;
        } else {
            // Handle publish failure (backpressure, not connected, etc.)
            System.err.println("Order publish failed: " + result.getErrorMessage());
            return 0;
        }
    }

    /**
     * Simplified execution report publishing with group handling.
     */
    public int publishExecutionReportEvent() {
        // Get encoder and populate basic fields
        ExecutionReportEncoderAdapter execEncoder = messagePublisher.getEncoder(ExecutionReportEncoderAdapter.class);
        
        execEncoder.executionId(1L)
                  .orderId(2L)
                  .executionType(ExecutionType.PARTIAL_FILL)
                  .executionStatus(ExecutionStatus.NULL_VAL)
                  .timestamp(System.nanoTime())
                  .symbol("TESTINST")
                  .side(Side.BUY)
                  .totalQuantity(200L)
                  .executedQuantity(100L)
                  .remainingQuantity(100L)
                  .avgPrice(1000L);

        // Handle trades group - same as before, but cleaner context
        ExecutionReportEncoderAdapter.TradesEncoder tradesEncoder = execEncoder.tradesCount(2);
        for (int index = 0; index < 2; index++) {
            tradesEncoder.next()
                    .tradeId(123L + tradeId++)
                    .tradePrice(100L + tradePrice++)
                    .tradeQuantity(50L)
                    .tradeTimestamp(System.nanoTime())
                    .counterpartyId(999L);
        }

        // Publish and handle result
        PublishResult result = messagePublisher.publish();
        
        if (result.isSuccess()) {
            return 1;
        } else {
            System.err.println("Execution report publish failed: " + result.getErrorMessage());
            return 0;
        }
    }

    private int checkConnectionStatus() {
        if (eventStreamSubscription.isConnected() && messagePublisher.isConnected()) {
            nodeAppStatus = RUNNING;
            return 1;
        } else {
            return 0;
        }
    }

    @Override
    public void onFragment(DirectBuffer buffer, int offset, int length, Header header) {
        messageHeaderDecoder.wrap(buffer, offset);
        // Message processing logic remains the same
    }

    @Override
    public void onClose() {
        aeron.close();
    }

    @Override
    public String roleName() {
        return "RefactoredPositionNodeApp";
    }
    
    /**
     * Gets the message publisher for testing or advanced usage.
     * @return the SbeMessagePublisher instance
     */
    public SbeMessagePublisher getMessagePublisher() {
        return messagePublisher;
    }
}
