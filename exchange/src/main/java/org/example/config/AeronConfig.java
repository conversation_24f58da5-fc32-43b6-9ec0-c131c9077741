package org.example.config;

import io.aeron.archive.Archive;
import io.aeron.archive.ArchiveThreadingMode;
import io.aeron.driver.MediaDriver;
import io.aeron.driver.ThreadingMode;
import org.agrona.concurrent.IdleStrategy;
import org.agrona.concurrent.SleepingIdleStrategy;

public class AeronConfig {

    public static final String IPC_CHANNEL = "aeron:ipc";
    public static final int COMMAND_STREAM_ID = 100;
    public static final int EVENT_STREAM_ID = 101;
    public static final int SEQUENCER_EVENT_STREAM_ID = 102;

    public static final String MEDIA_DRIVER_DIRECTORY = "/Users/<USER>/core/media-driver";
    public static final String ARCHIVE_DIRECTORY = "/Users/<USER>/core/archive";
    public static final IdleStrategy IDLE_STRATEGY = new SleepingIdleStrategy();
    public static final String CONTROL_REQUEST_CHANNEL = "aeron:udp?endpoint=localhost:8010";
    public static final String CONTROL_RESPONSE_CHANNEL = "aeron:udp?endpoint=localhost:8011";
    public static final String REPLICATION_CHANNEL = "aeron:udp?endpoint=localhost:8020";

    public static final long EVENT_RECORDING_ID = 0L;
    public static final long SNAPSHOT_RECORD_ID = 1L;
    public static final int FRAGMENT_LIMIT = 1;

    public static final MediaDriver.Context MediaDriverContext = new MediaDriver.Context()
            .threadingMode(ThreadingMode.SHARED)
            .aeronDirectoryName(MEDIA_DRIVER_DIRECTORY)
            .spiesSimulateConnection(true)
            .dirDeleteOnStart(true)
            .ipcTermBufferLength(128 * 1024 * 1024)
            .errorHandler(Throwable::printStackTrace);

    public static final Archive.Context ArchiveContext = new Archive.Context()
            .threadingMode(ArchiveThreadingMode.DEDICATED)
            .deleteArchiveOnStart(true)
            .controlChannel(CONTROL_REQUEST_CHANNEL)
            .replicationChannel(REPLICATION_CHANNEL)
            .archiveDirectoryName(ARCHIVE_DIRECTORY)
            .aeronDirectoryName(MEDIA_DRIVER_DIRECTORY)
            .errorHandler(Throwable::printStackTrace);

}