/*
 * This source file was generated by the Gradle 'init' task
 */
package org.example;

import org.agrona.concurrent.AgentRunner;
import org.example.services.AeronMediaDriver;
import org.example.services.ArchiveNodeApp;
import org.example.services.PositionNodeApp;
import org.example.services.SequencerNodeApp;
import org.example.utils.AgentUtils;

public class App {
    public String getGreeting() {
        return "Hello World!";
    }

    public static void main(String[] args) {
        AgentRunner mediaDriverRunner = AgentUtils.startAgent(new AeronMediaDriver());
        AgentRunner archiveNodeAppRunner = AgentUtils.startAgent(new ArchiveNodeApp());
        AgentRunner sequencerNodeAppRunner = AgentUtils.startAgent(new SequencerNodeApp());
        AgentRunner positionNodeAppRunner = AgentUtils.startAgent(new PositionNodeApp());

        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            AgentUtils.stopRunner(positionNodeAppRunner);
            AgentUtils.stopRunner(sequencerNodeAppRunner);
            AgentUtils.stopRunner(archiveNodeAppRunner);
            AgentUtils.stopRunner(mediaDriverRunner); // Shut down MediaDriver last
            System.out.println("Shutdown complete.");

        }));
    }
}
