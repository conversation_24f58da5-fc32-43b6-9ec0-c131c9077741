<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<sbe:messageSchema xmlns:sbe="http://fixprotocol.io/2016/sbe"
                   xmlns:xi="http://www.w3.org/2001/XInclude"
                   package="org.example.sbe"
                   id="1"
                   version="1"
                   semanticVersion="1.0.0"
                   description="Basic SBE messages">
    <types>
        <composite name="messageHeader" description="Message header">
            <type name="blockLength" primitiveType="uint16"/>
            <type name="templateId" primitiveType="uint16"/>
            <type name="schemaId" primitiveType="uint16"/>
            <type name="version" primitiveType="uint16"/>
            <ref name="sequence" type="Sequence"/>
        </composite>
        <composite name="varStringEncoding">
            <type name="length" primitiveType="uint32" maxValue="1073741824"/>
            <type name="varData" primitiveType="uint8" length="0" characterEncoding="UTF-8"/>
        </composite>
        <composite name="groupSizeEncoding" description="Repeating group dimensions.">
            <type name="blockLength" primitiveType="uint16"/>
            <type name="numInGroup" primitiveType="uint16"/>
        </composite>

        <enum name="OrderType" encodingType="int32">
            <validValue name="MARKET">1</validValue>
            <validValue name="LIMIT">2</validValue>
            <validValue name="TAKE_PROFIT_LIMIT">3</validValue>
        </enum>

        <enum name="ExecutionType" encodingType="int32">
            <validValue name="NEW">1</validValue>
            <validValue name="PARTIAL_FILL">2</validValue>
            <validValue name="FILL">3</validValue>
            <validValue name="CANCELED">4</validValue>
            <validValue name="REJECTED">5</validValue>
        </enum>

        <enum name="ExecutionStatus" encodingType="int32">
            <validValue name="PENDING">1</validValue>
            <validValue name="EXECUTED">2</validValue>
            <validValue name="REJECTED">3</validValue>
        </enum>

        <enum name="Side" encodingType="int32">
            <validValue name="BUY">1</validValue>
            <validValue name="SELL">2</validValue>
        </enum>

        <type name="Timestamp" primitiveType="int64"/>
        <type name="Sequence" primitiveType="uint64"/>
        <type name="ExecutionId" primitiveType="uint64"/>
        <type name="OrderId" primitiveType="uint64"/>
        <type name="TradeId" primitiveType="uint64"/>
        <type name="CounterpartyId" primitiveType="uint64"/>
        <type name="Symbol" primitiveType="char" length="8" semanticType="Symbol"/>
        <type name="Quantity" primitiveType="uint64"/>
        <type name="Price" primitiveType="uint64"/>
        <type name="ModelYear" primitiveType="uint16" semanticType="ModelYear"/>
        <type name="VehicleCode" primitiveType="char" length="6" semanticType="VehicleCode"/>
        <type name="someNumbers" primitiveType="int32" length="5" semanticType="SomeArray"/>
        <set name="OptionalExtras" encodingType="uint8" semanticType="Extras">
            <choice name="sunRoof">0</choice>
            <choice name="sportsPack">1</choice>
            <choice name="cruiseControl">2</choice>
        </set>
    </types>

    <sbe:message name="Order" id="4" description="Simple sample">
        <field name="orderType" id="1" type="OrderType"/>
        <field name="accountMsb" id="2" type="uint64"/>
        <field name="accountLsb" id="3" type="uint64"/>
        <field name="quantity" id="4" type="uint64"/>
        <field name="price" id="5" type="uint64"/>
    </sbe:message>

    <sbe:message name="ExecutionReport" id="5" description="Execution report for order processing">
        <!-- Basic Information Fields -->
        <field name="executionId" id="1" type="ExecutionId"/>
        <field name="orderId" id="2" type="OrderId"/>
        <field name="executionType" id="3" type="ExecutionType"/>
        <field name="executionStatus" id="4" type="ExecutionStatus" presence="optional" />
        <field name="timestamp" id="5" type="Timestamp"/>
        <field name="symbol" id="6" type="Symbol"/>
        <field name="side" id="7" type="Side"/>
        <field name="totalQuantity" id="8" type="Quantity"/>
        <field name="executedQuantity" id="9" type="Quantity"/>
        <field name="remainingQuantity" id="10" type="Quantity"/>
        <field name="avgPrice" id="11" type="Price"/>

        <!-- Multiple Trade Information (Repeating Group) -->
        <group name="trades" id="20" dimensionType="groupSizeEncoding">
            <field name="tradeId" id="21" type="TradeId"/>
            <field name="tradePrice" id="22" type="Price"/>
            <field name="tradeQuantity" id="23" type="Quantity"/>
            <field name="tradeTimestamp" id="24" type="Timestamp"/>
            <field name="counterpartyId" id="25" type="CounterpartyId"/>
        </group>
    </sbe:message>

</sbe:messageSchema>