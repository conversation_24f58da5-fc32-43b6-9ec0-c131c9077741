package org.example.messaging;

import io.aeron.Publication;
import org.agrona.concurrent.UnsafeBuffer;
import org.example.sbe.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.nio.ByteBuffer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Test class for SbeMessagePublisher functionality.
 */
public class SbeMessagePublisherTest {
    
    @Mock
    private Publication mockPublication;
    
    private SbeMessagePublisher publisher;
    
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        when(mockPublication.isConnected()).thenReturn(true);
        publisher = new SbeMessagePublisher(mockPublication);
    }
    
    @Test
    void testGetOrderEncoder() {
        // Test getting OrderEncoder
        OrderEncoderAdapter orderEncoder = publisher.getEncoder(OrderEncoderAdapter.class);
        
        assertNotNull(orderEncoder);
        assertEquals(1, publisher.getCacheSize());
        
        // Test that subsequent calls return the same cached instance
        OrderEncoderAdapter orderEncoder2 = publisher.getEncoder(OrderEncoderAdapter.class);
        assertSame(orderEncoder, orderEncoder2);
        assertEquals(1, publisher.getCacheSize()); // Cache size should remain 1
    }
    
    @Test
    void testGetExecutionReportEncoder() {
        // Test getting ExecutionReportEncoder
        ExecutionReportEncoderAdapter execEncoder = publisher.getEncoder(ExecutionReportEncoderAdapter.class);
        
        assertNotNull(execEncoder);
        assertEquals(1, publisher.getCacheSize());
    }
    
    @Test
    void testMultipleEncoderTypes() {
        // Test caching multiple encoder types
        OrderEncoderAdapter orderEncoder = publisher.getEncoder(OrderEncoderAdapter.class);
        ExecutionReportEncoderAdapter execEncoder = publisher.getEncoder(ExecutionReportEncoderAdapter.class);
        
        assertNotNull(orderEncoder);
        assertNotNull(execEncoder);
        assertEquals(2, publisher.getCacheSize());
        
        // Verify they are different instances
        assertNotSame(orderEncoder, execEncoder);
    }
    
    @Test
    void testPublishOrderMessage() {
        // Mock successful publication
        when(mockPublication.offer(any(UnsafeBuffer.class), eq(0), anyInt())).thenReturn(100L);
        
        // Get encoder and populate with data
        OrderEncoderAdapter orderEncoder = publisher.getEncoder(OrderEncoderAdapter.class);
        orderEncoder.orderType(OrderType.LIMIT)
                   .accountMsb(1L)
                   .accountLsb(2L)
                   .quantity(1000L)
                   .price(50L);
        
        // Publish the message
        PublishResult result = publisher.publish();
        
        assertTrue(result.isSuccess());
        assertEquals(100L, result.getPosition());
        
        // Verify publication was called
        verify(mockPublication).offer(any(UnsafeBuffer.class), eq(0), anyInt());
    }
    
    @Test
    void testPublishExecutionReportMessage() {
        // Mock successful publication
        when(mockPublication.offer(any(UnsafeBuffer.class), eq(0), anyInt())).thenReturn(200L);
        
        // Get encoder and populate with data
        ExecutionReportEncoderAdapter execEncoder = publisher.getEncoder(ExecutionReportEncoderAdapter.class);
        execEncoder.executionId(1L)
                  .orderId(2L)
                  .executionType(ExecutionType.PARTIAL_FILL)
                  .executionStatus(ExecutionStatus.NULL_VAL)
                  .timestamp(System.nanoTime())
                  .symbol("TESTINST")
                  .side(Side.BUY)
                  .totalQuantity(200L)
                  .executedQuantity(100L)
                  .remainingQuantity(100L)
                  .avgPrice(1000L);
        
        // Publish the message
        PublishResult result = publisher.publish();
        
        assertTrue(result.isSuccess());
        assertEquals(200L, result.getPosition());
        
        // Verify publication was called
        verify(mockPublication).offer(any(UnsafeBuffer.class), eq(0), anyInt());
    }
    
    @Test
    void testPublishBackpressure() {
        // Mock backpressure
        when(mockPublication.offer(any(UnsafeBuffer.class), eq(0), anyInt())).thenReturn(Publication.BACK_PRESSURED);
        
        OrderEncoderAdapter orderEncoder = publisher.getEncoder(OrderEncoderAdapter.class);
        orderEncoder.orderType(OrderType.LIMIT);
        
        PublishResult result = publisher.publish();
        
        assertFalse(result.isSuccess());
        assertEquals("Publication back pressure", result.getErrorMessage());
    }
    
    @Test
    void testPublishNotConnected() {
        // Mock not connected
        when(mockPublication.isConnected()).thenReturn(false);
        
        OrderEncoderAdapter orderEncoder = publisher.getEncoder(OrderEncoderAdapter.class);
        
        PublishResult result = publisher.publish();
        
        assertFalse(result.isSuccess());
        assertEquals("Publication not connected", result.getErrorMessage());
    }
    
    @Test
    void testPublishWithoutEncoder() {
        PublishResult result = publisher.publish();
        
        assertFalse(result.isSuccess());
        assertTrue(result.getErrorMessage().contains("No encoder configured"));
    }
    
    @Test
    void testClearCache() {
        // Add some encoders to cache
        publisher.getEncoder(OrderEncoderAdapter.class);
        publisher.getEncoder(ExecutionReportEncoderAdapter.class);
        assertEquals(2, publisher.getCacheSize());
        
        // Clear cache
        publisher.clearCache();
        assertEquals(0, publisher.getCacheSize());
    }
    
    @Test
    void testIsConnected() {
        when(mockPublication.isConnected()).thenReturn(true);
        assertTrue(publisher.isConnected());
        
        when(mockPublication.isConnected()).thenReturn(false);
        assertFalse(publisher.isConnected());
    }
}
